const mongoose = require('mongoose');

// Services
const manageReportService = require('../services/manage-report.service');
const memberService = require('../services/member.service');
const projectService = require('../services/project.service');
const reportQuestionService = require('../services/report-question.service');
const reportQuestionAnswerService = require('../services/report-question-answer.service');
const locationService = require('../services/location.service');
const assetService = require('../services/asset.service');
const scopeService = require('../services/scope.service');
const userReportService = require('../services/user-report.service');
const { exportReportPDF } = require('../services/pdf-template.service');
const dprService = require('../services/dpr.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const { successResponse, errorResponse } = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const { transactionOptions } = require('../utils/json-format.utils');
const commonfunctionUtils = require('../utils/common-function.utils');
const { generateProjectTrackerExcel } = require('../utils/export-excel.util');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Create Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.createReport = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOption = { ...transactionOptions };
  try {
    let { locations, assets, scope, ...reqData } = req.body;

    reqData.account = req.userData.account;
    reqData.createdBy = req.userData._id;

    const exist = await manageReportService.getSingleReportByFilter({
      title: { $regex: new RegExp(`^${reqData.title}$`, 'i') },
      project: reqData.project,
      account: reqData.account,
      deletedAt: null,
    });
    if (exist) {
      return res.status(422).json(
        errorResponse(constantUtils.REPORT_EXIST, {
          error: [
            {
              title: constantUtils.REPORT_EXIST,
            },
          ],
        })
      );
    }
    session.startTransaction(transactionOption);
    const reportResponse = await manageReportService.createReport(reqData, session);
    if (reportResponse) {
      await this.handleLocations(locations, reportResponse._id, res);
      await this.handleAssets(assets, reportResponse._id, res);
      await this.handleScope(scope, reportResponse._id, res);
      await this.commonUpdateSyncApiManage(req.userData.account);
    }
    if (reqData.reportId) {
      return await this.cloneReportFunction(reqData, reportResponse, res, session);
    }
    await session.commitTransaction();
    session.endSession();
    return res.status(200).json(successResponse(constantUtils.CREATE_REPORT, reportResponse));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * location Function
 *
 * @param {*} locations
 * @param {*} reportId
 * @returns
 */
exports.handleLocations = async (locations, reportId, res) => {
  if (locations) {
    for (let location of locations) {
      try {
        await locationService.pushReportInLocation(location, reportId);
      } catch (error) {
        return res.status(500).json(errorResponse(error.message));
      }
    }
  }
};
/**
 * asset Function
 *
 * @param {*} assets
 * @param {*} reportId
 * @returns
 */
exports.handleAssets = async (assets, reportId, res) => {
  if (assets) {
    for (let asset of assets) {
      try {
        await assetService.pushReportInAsset(asset, reportId);
      } catch (error) {
        return res.status(500).json(errorResponse(error.message));
      }
    }
  }
};
/**
 * scope Function
 *
 * @param {*} scope
 * @param {*} reportId
 * @returns
 */
exports.handleScope = async (scope, reportId, res) => {
  if (scope) {
    try {
      await scopeService.pushReportInScope(scope, reportId);
    } catch (error) {
      return res.status(500).json(errorResponse(error.message));
    }
  }
};

/**
 * cloneReport Function
 *
 * @param {*} reqData
 * @param {*} reportResponse
 * @param {*} res
 * @param {*} session
 * @returns
 */

exports.cloneReportFunction = async (reqData, reportResponse, res, session) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(reqData.reportId)) {
      return res.status(400).json(errorResponse(constantUtils.INVALID_REPORT_ID));
    }
    const filter = {
      _id: commonUtils.toObjectId(reqData.reportId),
      account: commonUtils.toObjectId(reqData.account),
      deletedAt: null,
    };

    const [report] = await this.getReportWithQuestion(filter);

    if (!report) {
      return res.status(400).json(errorResponse(constantUtils.REPORT_NOT_EXIST));
    }
    if (!report.reportQuestions.length) {
      await session.commitTransaction();
      session.endSession();
      return res.status(200).json(successResponse(constantUtils.CREATE_REPORT, reportResponse));
    }

    for (let question of report.reportQuestions) {
      const questionObj = {
        title: question?.title,
        sortOrder: question?.sortOrder,
        duration: question?.duration,
        weight: question?.weight,
        isRequired: question?.isRequired,
        supportedContent: question?.supportedContent,
        report: reportResponse._id,
        account: reqData.account,
        createdBy: reqData.createdBy,
      };
      const reportQuestionResponse = await reportQuestionService.createReportQuestion(
        questionObj,
        session
      );
      const answerArray = [];
      for (let answer of question.answerTypes) {
        if (!answer.title || answer.title.length !== answer.numberOfAnswers) {
          await session.abortTransaction();
          session.endSession();
          return res.status(400).json(errorResponse(constantUtils.INCORRECT_ANSWER_TITLES));
        }
        const answerObj = {
          title: answer?.title,
          reportQuestion: reportQuestionResponse?._id,
          parameterType: answer?.parameterType._id,
          report: reportResponse._id,
          option: answer?.option,
          range: answer?.range,
          numberOfAnswers: answer?.numberOfAnswers,
          account: reqData.account,
          createdBy: reqData.createdBy,
        };
        answerArray.push(answerObj);
      }
      await reportQuestionAnswerService.insertManyReportQuestionAnswer(answerArray, session);
    }
    await session.commitTransaction();
    session.endSession();

    return res.status(200).json(successResponse(constantUtils.CREATE_REPORT, reportResponse));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Reports
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReports = async (req, res) => {
  try {
    let { projectStatus, project } = req.query;
    let { filter, page, perPage, sort } = await commonfunctionUtils.getFilterAndPaginationParams(
      req
    );

    // Add project filter if project status is provided
    if (projectStatus && project === 'all') {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter
      );
    }

    delete filter?.projectStatus;

    const responseData = await manageReportService.getReportWithQuestion(
      filter,
      page,
      perPage,
      sort,
      false
    );

    return res.status(200).json(successResponse(constantUtils.REPORT_LIST, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Update Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReport = async (req, res) => {
  try {
    let { locations, assets, scope, ...reqData } = req.body;
    let { id } = req.params;

    let filter = {
      _id: id,
      account: req.userData.account,
      deletedAt: null,
    };

    const exists = await manageReportService.getSingleReportByFilter(filter);
    if (!exists) {
      return res.status(400).json(errorResponse(constantUtils.NO_REPORT));
    }
    const result = await this.validateReportPublish(req, exists, res);
    if (result?.status === false) {
      return res.status(400).json(errorResponse(result.message));
    }
    const titleResult = await this.validateTitleUniqueness(req, reqData, id, exists, res);
    if (titleResult?.status === false) {
      return res.status(422).json(
        errorResponse(titleResult.message, {
          error: [
            {
              title: titleResult.message,
            },
          ],
        })
      );
    }
    // Update Report
    reqData.updateAt = new Date();
    reqData.updatedBy = req.userData._id;

    const responseData = await manageReportService.updateReportById(id, reqData);

    let reportResponse = {};
    //Update Report in Location, Asset and Scope
    if (responseData) {
      let reportFilter = {
        _id: commonUtils.toObjectId(id),
        account: req.userData.account,
        deletedAt: null,
      };

      const getReport = await this.getSingleReportDataByFilterWithoutQuestion(reportFilter);

      await this.updateReportProcess(getReport, locations, assets, scope, id).then(async result => {
        if (result) {
          reportResponse = await this.getSingleReportDataByFilterWithoutQuestion(reportFilter);
        } else {
          reportResponse = getReport;
        }
      });
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constantUtils.UPDATE_REPORT, reportResponse));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Single Report By Filter Without Question
 *
 * @param {*} filter
 * @returns
 */
exports.getSingleReportDataByFilterWithoutQuestion = async filter => {
  const getReportData = await manageReportService.getReportWithQuestion(
    filter,
    null,
    null,
    null,
    false
  );

  return getReportData.length > 0 ? getReportData[0] : null;
};

/**
 * Update Report Process (Add or remove report id from location, asset and scope)
 *
 * @param {*} getReport
 * @param {*} locations
 * @param {*} assets
 * @param {*} scope
 * @param {*} id
 * @returns
 */
exports.updateReportProcess = async (getReport, locations, assets, scope, id) => {
  try {
    // Process locations
    if (locations) {
      if (getReport && getReport.locations.length > 0) {
        for (let locationData of getReport.locations) {
          await locationService.pullReportInLocation(locationData._id, id);
        }
      }
      for (let location of locations) {
        await locationService.pushReportInLocation(location, id);
      }
    }

    // Process assets
    if (assets) {
      if (getReport && getReport.assets.length > 0) {
        for (let assetsData of getReport.assets) {
          await assetService.pullReportInAsset(assetsData._id, id);
        }
      }
      for (let asset of assets) {
        await assetService.pushReportInAsset(asset, id);
      }
    }

    // Process scope
    if (scope) {
      if (getReport && getReport.scope.length > 0) {
        for (let scopeData of getReport.scope) {
          await scopeService.pullReportInScope(scopeData._id, id);
        }
      }
      await scopeService.pushReportInScope(scope, id);
    }
    return true;
  } catch (error) {
    console.error(constantUtils.ERROR_DURING_REPORT_UPDATE, error);
    return false;
  }
};

/**
 * Soft Delete Report
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.softDeleteReport = async (req, res) => {
  try {
    let { id } = req.params;

    let filter = {
      _id: id,
      account: req.userData.account,
      deletedAt: null,
    };

    const exists = await manageReportService.getSingleReportByFilter(filter);
    if (!exists) {
      return res.status(400).json(errorResponse(constantUtils.NO_REPORT));
    }

    let reqData = {
      deletedBy: req.userData._id,
      deletedAt: new Date(),
    };

    const responseData = await manageReportService.updateReportById(id, reqData);

    if (responseData) {
      await this.commonUpdateSyncApiManage(req.userData.account);
    }

    return res.status(200).json(successResponse(constantUtils.DELETE_REPORT, responseData));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Report By Id
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReportById = async (req, res) => {
  try {
    let { id } = req.params;

    let filter = {
      _id: commonUtils.toObjectId(id),
      account: req.userData.account,
      deletedAt: null,
    };

    let getReport = await this.getReportWithQuestion(filter);
    getReport[0].totalQuestionDuration = getReport.reduce((total, report) => {
      const reportDuration = report.reportQuestions.reduce((sum, question) => {
        return sum + (parseFloat(question.duration) || 0);
      }, 0);
      return total + reportDuration;
    }, 0);

    if (getReport.length === 0) {
      return res.status(400).json(errorResponse(constantUtils.NO_REPORT));
    }

    return res.status(200).json(successResponse(constantUtils.GET_REPORT, getReport[0]));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get Report Question Config
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReportQuestionConfig = async (req, res) => {
  try {
    let filter = {
      account: req.userData.account,
      isPublish: true,
      deletedAt: null,
    };

    if (req.userData.role.isAssignAllProjects) {
      const allProjects = await projectService.getAllProjects({
        account: req.userData.account,
        deletedAt: null,
      });
      if (allProjects.length > 0) {
        filter['project'] = { $in: allProjects.map(project => project._id) };
      }
    } else {
      const getMember = await memberService.getAllMember({
        user: req.userData._id,
        account: req.userData.account,
        deletedAt: null,
      });

      if (getMember?.length > 0) {
        filter['project'] = {
          $in: getMember.map(item => item.project._id),
        };
      }
    }
    const reportQuestionAnswers = await this.getConfigReportWithQuestion(filter);

    return res
      .status(200)
      .json(successResponse(constantUtils.GET_REPORT_QUESTION_ANSWERS, reportQuestionAnswers));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Project Wise Report Calculation
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectWiseReportCalculation = async (req, res) => {
  try {
    const { account } = req.userData;
    let { project } = req.query;
    let filter = {
      account,
      deletedAt: null,
    };

    if (project && commonUtils.isValidId(project)) {
      filter._id = commonUtils.toObjectId(project);
    }

    const getReportCal = await projectService.reportCalculation(filter);

    let finalCal = project && getReportCal.length > 0 ? getReportCal[0] : getReportCal;

    return res.status(200).json(successResponse(constantUtils.GET_REPORT_CALCULATION, finalCal));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Project Tracker Report Calculation and Summary
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectTracker = async (req, res) => {
  try {
    const { dprId, summary } = req.query;
    let dpr, today;

    if (summary) {
      dpr = await dprService.getLatestDpr({
        _id: dprId,
        account: req.userData.account,
        deletedAt: null,
      });

      if (!dpr) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constantUtils.NO_DPR));
      }
    }
    if (dpr?.dprDate) {
      const dateOnly = new Date(dpr?.dprDate).toISOString().split('T')[0];
      today = new Date(`${dateOnly}T23:59:59.999Z`);
    }
    const projectId = req.query.summary ? dpr.project : req.params.projectId;
    let result = await this.projectTrackerData(req, projectId, today);

    return res.status(200).json(successResponse(constantUtils.GET_PROJECT_TRACKER, result));
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

exports.validateReportPublish = async (req, exists) => {
  if ('isPublish' in req.body) {
    if (
      exists.type === global.constant.ASSET_PER_LOCATION ||
      exists.type === global.constant.MULTIPLE_ASSETS
    ) {
      let [assetExist] = await assetService.getAllAsset({
        account: req.userData.account,
        reports: { $in: [commonUtils.toObjectId(exists._id)] },
      });

      if (!assetExist) {
        return {
          status: false,
          message: constantUtils.ASSIGN_ASSET,
        };
      }
    } else if (exists.type === global.constant.LOCATION) {
      let [locationExist] = await locationService.getAllData({
        account: req.userData.account,
        reports: { $in: [commonUtils.toObjectId(exists._id)] },
      });
      if (!locationExist) {
        return {
          status: false,
          message: constantUtils.ASSIGN_LOCATION,
        };
      }
    }
  }
};

exports.validateTitleUniqueness = async (req, reqData, id, exists) => {
  if ('title' in reqData) {
    const checkReport = await manageReportService.getSingleReportByFilter({
      _id: { $ne: id },
      title: { $regex: new RegExp(`^${reqData.title}$`, 'i') },
      project: exists.project._id,
      account: req.userData.account,
      deletedAt: null,
    });
    if (checkReport) {
      return {
        status: false,
        message: constantUtils.REPORT_EXIST,
      };
    }
  }
};

/**
 * Get Report With Questions
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReportWithQuestion = async filter => {
  let report = await manageReportService.getOneReportId(filter);

  let reportQuestionIds = await reportQuestionService.getReportQuestion(report[0]._id);
  // eslint-disable-next-line no-undef
  let responseData = await Promise.all(
    reportQuestionIds.map(async item => {
      let reportDetails = await reportQuestionAnswerService.getReportQuestionAnswer(item._id);
      item.answerTypes = reportDetails;
      return item;
    })
  );
  report[0].reportQuestions = responseData;
  return report;
};

/**
 * Get config Report With Questions
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getConfigReportWithQuestion = async (filter, page = null, perPage = null, sort = null) => {
  let reportsIds = await manageReportService.getReportsIDs(filter, page, perPage, sort);
  // eslint-disable-next-line no-undef
  let responseData = await Promise.all(
    reportsIds.map(async item => {
      let [reportDetails] = await manageReportService.getReportDetails(item._id);
      let [questions] = await manageReportService.getReportDetailsWithQuestions(item._id);
      if (reportDetails && questions) {
        const reportQuestions = questions.reportQuestions;
        reportDetails = { ...reportDetails, reportQuestions };
        item = reportDetails;
        return item;
      }
    })
  );
  responseData = responseData.filter(id => id?._id);
  return responseData;
};

/**
 * Get project tracker excel
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectTrackerExcel = async (req, res) => {
  try {
    const projectId = req.params.projectId;
    let result = await this.projectTrackerData(req, projectId);

    const project = await projectService.getProjectById(projectId, req.userData.account);
    const fileName = await commonUtils.formatFileName(`project-Tracker_${project.title}`);
    result.projectData = project;
    result.projectData.projectDate = req.query.projectDate;
    await generateProjectTrackerExcel(result, res, fileName);
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Get project tracker data
 *
 * @param {*} req
 * @param {*} projectId
 * @returns
 */
exports.projectTrackerData = async (req, projectId, dprDate = null) => {
  const scopeFilter = {
    project: commonUtils.toObjectId(projectId),
    account: req.userData.account,
    deletedAt: null,
  };

  if (dprDate) {
    scopeFilter.createdAt = { $lte: dprDate };
  }
  const filter = {
    project: commonUtils.toObjectId(projectId),
    account: req.userData.account,
    deletedAt: null,
  };

  if (dprDate) {
    filter.createdAt = { $lte: dprDate };
  }

  // get All scopes of project
  // get All locations of project which is connected with atleast 1 report
  // get All assets of project which is connected with atleast 1 report

  // First get scope data to extract report IDs
  let scopeData = await scopeService.getProjectTrackerContent(scopeFilter);
  scopeData = scopeData.filter(scope => scope.reports.length > 0);

  // Extract all report IDs from scope data
  const reportIds = [];
  scopeData.forEach(scope => {
    scope.reports.forEach(report => {
      if (!reportIds.some(id => id.toString() === report._id.toString())) {
        reportIds.push(commonUtils.toObjectId(report._id));
      }
    });
  });

  console.log({ filter, reportIds });
  // eslint-disable-next-line no-undef
  let [locationData, assetsData] = await Promise.all([
    locationService.getAllLocationByProjectForProjectTracker(filter),
    assetService.getAssetByProjectForProjectTracker(filter, reportIds),
  ]);

  // When asset is used in report but the lcoation linked in fromLcoation and toLcoation is not used in report
  // Then adding that location object in lcoationData
  // Identify exceptional locations
  let exceptionalLocations = [];
  assetsData.forEach(asset => {
    ['fromLocation', 'toLocation'].forEach(locType => {
      if (
        asset[locType] &&
        !locationData.some(l => l._id.toString() === asset[locType]._id.toString())
      ) {
        exceptionalLocations.push({
          ...asset[locType],
          reports: [],
          assetsReports: asset.reports,
        });
      }
    });
  });

  const filteredLocationData = exceptionalLocations.filter(
    (item, index) =>
      exceptionalLocations.findIndex(
        loc => loc._id.toString() === item._id.toString() && loc.assetsReports.length > 0
      ) === index
  );

  locationData = [...locationData, ...filteredLocationData];

  let totalDurationsOfAllReports = 0;

  // Calculate requiredReport as per lcoation and asset
  scopeData = scopeData.map(item => ({
    ...item,
    reports: item.reports.map(report => {
      const type = report.type;
      let requiredReports = 0;
      let assets = null;
      switch (type) {
        case 'location': {
          const reportRequired = locationData.filter(
            l => l.reports.filter(r => r._id.toString() === report._id.toString()).length > 0
          );

          if (reportRequired.length > 0) {
            requiredReports = reportRequired.length;
          }
          break;
        }

        case 'asset_per_location': {
          // find all location required for this report
          const locationLinkedWithReport = locationData.filter(
            l => l.reports.filter(r => r._id.toString() === report._id.toString()).length > 0
          );

          //find all possible reports based on location and assets combinations.
          let possibleReport = [];
          locationLinkedWithReport.forEach(l => {
            const linkedAssetWithLocation = assetsData.filter(
              a =>
                l._id.toString() === a.fromLocation._id.toString() ||
                l._id.toString() === a.toLocation._id.toString()
            );
            possibleReport.push(...linkedAssetWithLocation);
          });
          requiredReports = possibleReport.length;

          // remove duplicate assets
          // eslint-disable-next-line no-undef
          assets = [...new Set(possibleReport)];
          break;
        }

        case 'multiple_assets': {
          let reportRequired;
          reportRequired = assetsData.filter(
            a => a.reports.filter(r => r._id.toString() === report._id.toString()).length > 0
          );

          if (reportRequired.length > 0) {
            const possibleReport = [];
            reportRequired.forEach(item => {
              possibleReport.push(`${item._id}-${item.fromLocation._id}`);
              possibleReport.push(`${item._id}-${item.toLocation._id}`);
            });
            // Filter unique reports
            // eslint-disable-next-line no-undef
            const uniqueKey = [...new Set(possibleReport)];
            requiredReports = uniqueKey.length;
            assets = reportRequired;
          }
          break;
        }
      }

      let totalDurationReport = report.reportDurations * requiredReports;

      totalDurationsOfAllReports += totalDurationReport;
      return {
        ...report,
        requiredReports,
        assets,
        totalDurationReport,
      };
    }),
  }));

  scopeData = scopeData.map(item => ({
    ...item,
    reports: item.reports.map(report => ({
      ...report,
      weightage: (report.totalDurationReport / totalDurationsOfAllReports) * 100,
    })),
  }));

  const rowFormat = {
    _id: null,
    locations: null,
    assets: [
      {
        _id: null,
        title: null,
      },
    ],
  };

  const customReportFormat = (report, isRequired = false, createdReports = {}, duration = 0) => {
    const reportDuration = createdReports.totalDuration || duration || 0;
    const completedDuration = createdReports.totalAnsweredDuration || 0;

    let completion = 0;
    if (reportDuration > 0) {
      completion = (completedDuration / reportDuration) * 100;
    }

    return {
      _id: report._id,
      title: report.title,
      type: report.type,
      createdAt: report.createdAt,
      status: createdReports?.status || [],
      reportDuration,
      completedDuration,
      completedDate: createdReports.updatedAt || null,
      completion,
      isRequired: isRequired,
    };
  };

  // eslint-disable-next-line no-undef
  const projectTrackerData = await Promise.all(
    locationData.map(async location => {
      const tempRawFormat = JSON.parse(JSON.stringify(rowFormat));
      tempRawFormat._id = location._id;
      tempRawFormat.locations = location.title;
      tempRawFormat.reportList = [];

      // Find assets that match fromLocation or toLocation of location
      const filterAssets = assetsData.filter(
        asset =>
          asset.fromLocation._id.toString() === location._id.toString() ||
          asset.toLocation._id.toString() === location._id.toString()
      );
      tempRawFormat.assets = filterAssets.map(asset => ({
        _id: asset._id,
        title: asset.cableName,
        fromLocation: asset.fromLocation,
        toLocation: asset.toLocation,
      }));

      // Flatten the scope reports into a single array
      const flatReports = scopeData.flatMap(scope => scope.reports.map(report => ({ ...report })));

      // Create an array of promises to ensure order is maintained
      const reportPromises = flatReports.map(async report => {
        const tempReport = {
          _id: report._id,
          title: report.title,
          type: report.type,
          createdAt: report.createdAt,
          reportList: [],
        };

        if (report.type === 'location') {
          const isReportLinkedWithLocation = location.reports.find(
            r => r._id.toString() === report._id.toString()
          );
          if (isReportLinkedWithLocation !== undefined) {
            const locationTypeFilter = {
              location: location._id,
              asset: [],
              report: { $in: [report._id] },
              account: req.userData.account,
              deletedAt: null,
            };

            if (dprDate) {
              locationTypeFilter.createdAt = { $lte: dprDate };
            }

            const locationTypeReports = await userReportService.getReportsForCompletion(
              locationTypeFilter,
              null,
              null,
              -1
            );

            if (locationTypeReports.length > 0) {
              const locationReport = customReportFormat(report, true, locationTypeReports[0]);
              tempReport.reportList.push(locationReport);
            } else {
              const locationReport = customReportFormat(report, true, {}, report.reportDurations);
              tempReport.reportList.push(locationReport);
            }
          } else {
            const locationReport = customReportFormat(report);
            tempReport.reportList.push(locationReport);
          }
        }
        // For Asset Per Location and Multiple Assets reports
        else if (filterAssets.length !== 0) {
          for (const asset of filterAssets) {
            let isRequiredReport;
            // For Asset Per Location
            if (report.type === 'asset_per_location') {
              // check the locations is requried for this report if report type is asset_per_location
              isRequiredReport = asset.reports.find(r => {
                const islocationLinkedWithReprot = location.reports.find(
                  lr => lr._id.toString() === report._id.toString()
                );
                if (!islocationLinkedWithReprot) return false;

                // check the asset is required for this report
                return r._id.toString() === report._id.toString();
              });
            }
            // For Multiple Assets
            else {
              // check the asset is required for this report
              isRequiredReport = asset.reports.find(
                r => r._id.toString() === report._id.toString()
              );
            }
            if (isRequiredReport !== undefined) {
              // Find reports that match asset (multiple assets, asset per location)
              let assetReportFilter = {
                'asset.asset': asset._id,
                report: { $in: [report._id] },
                account: req.userData.account,
                deletedAt: null,
              };

              if (dprDate) {
                assetReportFilter.createdAt = { $lte: dprDate };
              }

              // Add location filter if report type is asset_per_location
              assetReportFilter = {
                ...assetReportFilter,
                ...(report.type === 'asset_per_location' ? { location: location._id } : {}),
              };

              const assetTypeReports = await userReportService.getReportsForCompletion(
                assetReportFilter,
                null,
                null,
                -1
              );

              if (assetTypeReports.length > 0) {
                // Get the report which have totalAnsweredDuration which have max value
                let getAssetTypeReport = assetTypeReports[0];
                let tad = 0;
                for (const assetTypeReport of assetTypeReports) {
                  if (assetTypeReport.totalAnsweredDuration > tad) {
                    tad = assetTypeReport.totalAnsweredDuration;
                    getAssetTypeReport = assetTypeReport;
                  }
                }
                tempReport.reportList.push(customReportFormat(report, true, getAssetTypeReport));
              } else {
                tempReport.reportList.push(
                  customReportFormat(report, true, {}, report.reportDurations)
                );
              }
            } else {
              tempReport.reportList.push(customReportFormat(report));
            }
          }
        } else tempReport.reportList.push(customReportFormat(report));

        return tempReport;
      });

      // Await all report promises and assign to reportList in correct order
      // eslint-disable-next-line no-undef
      tempRawFormat.reportList = await Promise.all(reportPromises);
      return tempRawFormat;
    })
  );

  let totalCompletions = 0;

  scopeData = scopeData.map(scope => ({
    ...scope,
    reports: scope.reports.map(report => {
      let totalCompletedReportDuration = 0;
      projectTrackerData.forEach(projectTracker => {
        const reportIndex = projectTracker.reportList.findIndex(
          reportList => reportList._id.toString() === report._id.toString()
        );
        projectTracker.reportList[reportIndex].reportList.forEach(reportList => {
          if (reportList._id.toString() === report._id.toString()) {
            totalCompletedReportDuration += reportList.completedDuration;
          }
        });
      });

      totalCompletions +=
        (totalCompletedReportDuration / report.totalDurationReport) *
          (report.totalDurationReport / totalDurationsOfAllReports) || 0;

      return {
        ...report,
        totalCompletedReportDuration,
        reportCompletion: (totalCompletedReportDuration / report.totalDurationReport) * 100,
      };
    }),
  }));
  return {
    projectTrackerData,
    scopeData,
    totalDurationsOfAllReports,
    totalCompletions: totalCompletions * 100,
  };
};

/**
 * Get printable report titles
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPrintableReportTitles = async (req, res) => {
  try {
    let { reportId } = req.params;
    let { status } = req.query;

    let filter = {
      _id: commonUtils.toObjectId(reportId),
      account: req.userData.account,
      deletedAt: null,
    };

    let report = await manageReportService.getOneReportId(filter);

    if (report.length === 0) {
      return res.status(400).json(errorResponse(constantUtils.NO_REPORT));
    }

    let reportQuestionIds = await reportQuestionService.reportQuestions(report[0]._id);

    // eslint-disable-next-line no-undef
    let responseData = await Promise.all(
      reportQuestionIds.map(async item => {
        let reportDetails = await reportQuestionAnswerService.getPrintableReportQuestionAnswer(
          item._id,
          status == 'pdf' ? [true] : [true, false]
        );
        item.answerTypes = reportDetails;
        return item;
      })
    );

    let structuredReport = {
      _id: {
        report: {
          _id: report[0]._id,
          title: report[0].title,
          type: report[0].type,
          status: report[0].statue,
          isProgressable: report[0].isProgressable,
        },
        project: {},
        location: {},
        asset: [{}],
      },
      reportQuestions: responseData.map(response => {
        return {
          questionId: response._id,
          title: response.title,
          sortOrder: response.sortOrder,
          duration: response.duration,
          weight: response.weight,
          supportedContent: response.supportedContent,
          answerTypes: response.answerTypes.map(answerType => {
            return {
              _id: answerType._id,
              parameterType: answerType.parameterType,
              option: answerType.option,
              range: answerType.range,
              numberOfAnswers: answerType.numberOfAnswers,
              title: answerType.title,
              userAnswers: [],
            };
          }),
        };
      }),
    };

    report[0] = structuredReport;
    const modifyedResponse = await this.modifyUserReportData(report[0]);

    modifyedResponse.exportReportStatus = status;
    return await exportReportPDF(modifyedResponse, res);
  } catch (error) {
    res.status(500).json(errorResponse(error.message));
  }
};

/**
 * Modify user report data
 *
 * @param {*} requestData
 * @returns
 */
exports.modifyUserReportData = async requestData => {
  let { _id, reportQuestions } = requestData;
  let prepareRepoprtData = {};

  for (let key in _id) {
    if (['project', 'report', 'location'].includes(key)) {
      prepareRepoprtData[key] = _id[key].title;
    }
    prepareRepoprtData.assets = _id.asset;
    prepareRepoprtData.userReportStatus = _id.userReportStatus;
  }

  let reportQuestionData = [];
  for (let key in reportQuestions) {
    let prepareQuestionData = {};
    prepareQuestionData.title = `${reportQuestions[key].title}`;
    prepareQuestionData.supportedContent = reportQuestions[key].supportedContent;

    let answerData = this.prepareReportAnswers(reportQuestions[key].answerTypes);

    prepareQuestionData.answers = answerData;
    reportQuestionData.push(prepareQuestionData);
  }

  prepareRepoprtData.reportQuestions = reportQuestionData;

  return prepareRepoprtData;
};

/**
 * Prepare report answers
 *
 * @param {*} answerTypes
 * @returns
 */
exports.prepareReportAnswers = answerTypes => {
  const answerData = [];

  for (let answerKey of answerTypes) {
    const titleData = {
      type: answerKey.parameterType.uniqueKey,
      name: answerKey.parameterType.name,
      options: answerKey.option?.length ? answerKey.option : undefined,
    };

    const userAnswerData = [];
    const answerTitleMap = {};
    const answeredIds = [];
    let isRequired, isPrintable;

    for (let title of answerKey.title) {
      answerTitleMap[title._id.toString()] = title.value;
      isPrintable = title.isPrintable;
      isRequired = title.isRequired;
    }

    for (let userAnswer of answerKey.userAnswers) {
      for (let answer of userAnswer.answers) {
        const titleIdStr = answer.answerTitleId.toString();
        const matchedValue = answerTitleMap[titleIdStr];

        if (matchedValue) {
          if (!answeredIds.includes(titleIdStr)) {
            answeredIds.push(titleIdStr);
          }
          userAnswerData.push({
            answerTitle: matchedValue,
            answer: answer.answer,
            isPrintable: isPrintable,
            isRequired: isRequired,
            _id: answer.answerTitleId,
          });
        }
      }
    }

    // Add unanswered titles
    for (let titleIdStr in answerTitleMap) {
      if (!answeredIds.includes(titleIdStr)) {
        userAnswerData.push({
          answerTitle: answerTitleMap[titleIdStr],
          answer: '',
          _id: titleIdStr,
        });
      }
    }

    userAnswerData.sort((a, b) => {
      const idA = a._id.toString();
      const idB = b._id.toString();

      return idA.localeCompare(idB);
    });

    titleData.answers = userAnswerData;
    answerData.push(titleData);
  }

  return answerData;
};

/**
 * Get detailed progress report
 *
 * @param {Object} req
 * @param {Object} res
 * @returns
 */
exports.reportsDetailedProgress = async (req, res) => {
  try {
    const { dprId } = req.params;

    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });

    if (!dpr) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(errorResponse(constantUtils.NO_DPR));
    }

    let sortOrder = req.query.sortOrder && req.query.sortOrder === 'asc' ? 1 : -1;

    const filter = {
      project: commonUtils.toObjectId(dpr.project),
      account: req.userData.account,
    };

    const dprDetailedProgressReport = await manageReportService.getDetailedProgressReportData(
      filter,
      dpr.dprDate,
      sortOrder
    );

    return res
      .status(HTTP_STATUS.OK)
      .json(
        successResponse(constantUtils.DETAILED_PROGRESS_REPORT_SUCCESS, dprDetailedProgressReport)
      );
  } catch (error) {
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(errorResponse(error));
  }
};

exports.commonUpdateSyncApiManage = async account => {
  await commonfunctionUtils.updateSyncApiManage({
    syncApis: ['reportUsers', 'reportConfig', 'allUserReports', 'reportNewFormConfig'],
    account,
  });
};
